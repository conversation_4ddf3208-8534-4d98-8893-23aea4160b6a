"""
Home presenter implementation for the FlatMate application.
"""

from PySide6.QtCore import QTimer
from PySide6.QtWidgets import QApplication

from ...core.services.logger import log
from ...gui.services.info_bar_service import InfoBarService
from .home_content import WELCOME_CONTENT
from .home_state import HomeState
from .home_view import HomeView


class HomePresenter:
    """Presenter for the home module."""

    def __init__(self, main_window):
        """Initialize the home presenter.

        Args:
            main_window: The main window instance
        """
        log.info("Initializing Home Presenter")
        self.view = HomeView()
        self.main_window = main_window
        self.state = HomeState()
        self.info_bar_service = InfoBarService.get_instance()
        log.debug("Home Presenter initialization complete")

    def _connect_signals(self):
        """Connect view signals to handlers."""
        log.debug("Connecting Home View signals")
        # These will now request transitions through the coordinator
        self.view.update_data_clicked.connect(
            lambda: self.request_transition("update_data")
        )
        self.view.categorize_clicked.connect(lambda: self.request_transition("categorize"))
        self.view.settings_clicked.connect(lambda: self.request_transition("settings"))
        self.view.quit_clicked.connect(self._on_quit_click)

    def initialize(self):
        """Initialize the home module."""
        log.info("Initializing Home Module")
        self._connect_signals()

        # Set up view in main window
        self.view.setup_in_main_window(self.main_window)

        # Test the centralized info bar with container approach
        self.info_bar_service.show()
        self.info_bar_service.publish_message("Home module loaded - Info bar positioned at bottom of center panel")

        if self.state.is_first_run:
            # First show the splash screen (logo only)
            self.view.show_splash_screen()
            # Then after a delay, show the welcome content
            QTimer.singleShot(
                2000, lambda: self.view.show_default_content(WELCOME_CONTENT)
            )
            self.state.mark_first_run_complete()
        else:
            # Just show the welcome content directly
            self.view.show_default_content(WELCOME_CONTENT)

        log.debug("Home Module initialization complete")

    def request_transition(self, module_name: str):
        """Request a transition to another module.

        This will be connected to the coordinator by the coordinator itself.

        Args:
            module_name: Name of the module to transition to
        """
        # This method will be connected to coordinator.transition_to
        # The implementation is provided by the coordinator
        pass

    def _on_quit_click(self):
        """Handle Quit button click."""
        QApplication.quit()

    def cleanup(self):
        """Clean up before being replaced."""
        if self.view:
            self.view.cleanup()
        log.debug("Home Module cleanup complete")
