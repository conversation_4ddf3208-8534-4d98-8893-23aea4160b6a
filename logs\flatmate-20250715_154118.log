2025-07-15 15:41:18 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 15:41:18 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 15:41:18 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 15:41:18 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 15:41:18 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 15:41:18 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 15:41:18 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 15:41:18 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 15:41:18 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 15:41:18 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 15:41:18 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 15:41:18 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 15:41:18 - [main] [INFO] - Application starting...
2025-07-15 15:41:20 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-15 15:41:20 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-15 15:41:20 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-15 15:41:20 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-15 15:41:20 - [flatmate.src.fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-15 15:41:20 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-15 15:41:20 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 15:41:20 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 15:41:20 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 15:41:20 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 15:41:20 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 15:41:20 - [main] [INFO] - 
=== Application Ready ===
2025-07-15 15:41:26 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-15 15:41:27 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-15 15:41:27 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 15:41:27 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-15 15:41:27 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-15 15:41:27 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-15 15:41:27 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Auto-loading from database with last query/filters...
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Restoring last used filters: {'start_date': datetime.date(2022, 1, 1), 'end_date': datetime.date(2024, 1, 1), 'account': 'test_account_123'}
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from DB for categorisation…
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Filters: {'start_date': datetime.date(2022, 1, 1), 'end_date': datetime.date(2024, 1, 1), 'account': 'test_account_123'}
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_start_date = 2022-01-01 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_start_date = 2022-01-01
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_end_date = 2024-01-01 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_end_date = 2024-01-01
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_account = test_account_123 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 15:41:27 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_account = test_account_123
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Saved last used filters for next session
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {'start_date': datetime.date(2022, 1, 1), 'end_date': datetime.date(2024, 1, 1), 'account_number': 'test_account_123'}
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Retrieved 0 transactions from database
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize.cat_presenter] [WARNING] - No transactions found in database matching the criteria
2025-07-15 15:41:27 - [flatmate.src.fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 0 rows
2025-07-15 15:41:50 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from DB for categorisation…
2025-07-15 15:41:50 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Filters: {}
2025-07-15 15:41:50 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions from database
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Converted to DataFrame with shape: (2099, 30)
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Applying default sort: amount (ascending)
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Successfully sorted by amount
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-15 15:41:51 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-15 15:41:53 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Account', 'Date', 'Details', 'Amount', 'Category', 'Tags', 'Notes']
2025-07-15 15:41:53 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2099 transactions in 2.9s (733.3 txns/s)
2025-07-15 15:41:53 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from DB for categorisation…
2025-07-15 15:41:53 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Filters: {}
2025-07-15 15:41:53 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions from database
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Converted to DataFrame with shape: (2099, 30)
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Applying default sort: amount (ascending)
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Successfully sorted by amount
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-15 15:41:54 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-15 15:42:05 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Account', 'Date', 'Details', 'Amount', 'Category', 'Tags', 'Notes']
2025-07-15 15:42:05 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2099 transactions in 11.9s (176.9 txns/s)
